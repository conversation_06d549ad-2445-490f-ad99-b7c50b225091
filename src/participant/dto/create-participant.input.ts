import { InputType, Field, PartialType, ID } from '@nestjs/graphql'
import {
    IsString,
    IsOptional,
    IsArray,
    ValidateNested,
    IsEmail,
    IsNumber,
    IsBoolean,
    IsDateString,
} from 'class-validator'
import { Type } from 'class-transformer'
import { CreatePersonalInfoInput } from '../../personal-info/dto/create-personal-info.input'
import { CreatePensionParametersInput } from '../../pension-parameters/dto/create-pension-parameter.input'
import { CreateSalaryEntryInput } from '../../salary-entry/dto/create-salary-entry.input'
import { CreateAnnualAccrualInput } from '../../annual-accrual/dto/create-annual-accrual.input'
import { CreateVoluntaryContributionInput } from '../../voluntary-contribution/dto/create-voluntary-contribution.input'
import { CreateConversionDetailsInput } from '../../conversion-details/dto/create-conversion-detail.input'

@InputType()
export class CreateNewAddressInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    street?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    houseNumber?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    postalCode?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    city?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    state?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    country?: string
}

// Partner Info Input
@InputType()
export class CreateNewPartnerInfoInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    firstName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    lastName?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    dateOfBirth?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    startDate?: string

    @Field()
    @IsBoolean()
    isCurrent: boolean

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isDeceased?: boolean = false
}

// Child Input
@InputType()
export class CreateNewChildInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    firstName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    lastName?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    dateOfBirth?: string

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isOrphan?: boolean = false

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isStudying?: boolean = false
}

// Employment Info Input
@InputType()
export class CreateNewEmploymentInfoInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    employeeId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    department?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    position?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    regNum?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    havNum?: number

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    startDate?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    endDate?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field(() => [CreateSalaryEntryInput], { nullable: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateSalaryEntryInput)
    @IsOptional()
    salaryEntries?: CreateSalaryEntryInput[]
}

// Pension Info Input
@InputType()
export class CreateNewPensionInfoInput {
    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    code?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    codeDescription?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    codeEffectiveDate?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualOldAgePension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualPartnersPension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    accruedGrossAnnualSinglesPension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    attainableGrossAnnualOldAgePension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    extraAccruedGrossAnnualOldAgePension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    extraAccruedGrossAnnualPartnersPension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    grossAnnualDisabilityPension?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    pensionBase?: number
}
//
// // Voluntary Contribution Input
// @InputType()
// export class CreateVoluntaryContributionInput {
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     amount?: number
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     accumulatedInterest?: number
//
//     @Field({ nullable: true })
//     @IsDateString()
//     @IsOptional()
//     date?: string
//
//     @Field({ nullable: true })
//     @IsString()
//     @IsOptional()
//     type?: string
// }
//
// // Annual Accrual Input
// @InputType()
// export class CreateAnnualAccrualInput {
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     employeeContributions?: number
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     employerContributions?: number
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     franchise?: number
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     monthlyBenefit?: number
// }

// Conversion Details Input
// @InputType()
// export class CreateConversionDetailsInput {
//     @Field({ nullable: true })
//     @IsDateString()
//     @IsOptional()
//     conversionDate?: string
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     conversionRate?: number
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     oldAgePensionIncrease?: number
//
//     @Field({ nullable: true })
//     @IsNumber()
//     @IsOptional()
//     partnerPensionIncrease?: number
// }

// Pension Data Input
@InputType()
export class CreateNewPensionDataInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field({ nullable: true })
    @IsDateString()
    @IsOptional()
    retirementDate?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    pensionableAmount?: number

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    totalContributions?: number

    @Field(() => [CreateVoluntaryContributionInput], { nullable: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateVoluntaryContributionInput)
    @IsOptional()
    voluntaryContributions?: CreateVoluntaryContributionInput[]

    @Field(() => CreateAnnualAccrualInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateAnnualAccrualInput)
    @IsOptional()
    annualAccrual?: CreateAnnualAccrualInput

    @Field(() => CreateConversionDetailsInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateConversionDetailsInput)
    @IsOptional()
    conversionDetails?: CreateConversionDetailsInput

    @Field(() => [CreatePensionParametersInput], { nullable: true })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreatePensionParametersInput)
    @IsOptional()
    pensionParameters?: CreatePensionParametersInput[]
}

// Main Create Participant Input
@InputType()
export class CreateParticipantInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string = 'active'

    @Field(() => CreatePersonalInfoInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreatePersonalInfoInput)
    @IsOptional()
    personalInfo?: CreatePersonalInfoInput

    @Field(() => CreateNewEmploymentInfoInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateNewEmploymentInfoInput)
    @IsOptional()
    employmentInfo?: CreateNewEmploymentInfoInput

    @Field(() => CreateNewPensionInfoInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateNewPensionInfoInput)
    @IsOptional()
    pensionInfo?: CreateNewPensionInfoInput

    @Field(() => CreateNewPensionDataInput, { nullable: true })
    @ValidateNested()
    @Type(() => CreateNewPensionDataInput)
    @IsOptional()
    pensionData?: CreateNewPensionDataInput
}

// Update Participant Input
@InputType()
export class UpdateParticipantInput extends PartialType(
    CreateParticipantInput
) {
    @Field(() => ID)
    @IsString()
    id: string
}

// Find All Participants Input
@InputType()
export class FindAllParticipantsInput {
    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    skip?: number = 0

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    take?: number = 20

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    searchTerm?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortBy?: string = 'createdAt'

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    sortOrder?: 'asc' | 'desc' = 'desc'
}
